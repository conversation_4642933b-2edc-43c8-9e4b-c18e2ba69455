import { apiClient } from "./clients";

const PATIENT_EXPERIENCE_ENDPOINTS = {
	base: "/api/v1/patient-experience/feedbacks",
	stats: "/api/v1/patient-experience/stats",
	summaries: "/api/v1/patient-experience/summaries",
	detail: (responseUUId: string) =>
		`/api/v1/patient-experience/feedbacks/${responseUUId}`,
} as const;

export interface PatientExperienceFilters {
	organization_id?: number;
	location_ids?: number[];
	category_ids?: number[];
	station_ids?: number[];
	service_ids?: number[];
	page?: number;
	per_page?: number;
}

export interface PatientFeedback {
	id: string;
	patient_name: string;
	patient_avatar: string;
	station_name: string;
	location_name: string;
	service_name: string;
	rating: number;
	date: string;
}

export interface PatientExperienceResponse {
	success: boolean;
	data: {
		feedbacks: PatientFeedback[];
	};
	message: string;
}

export interface PatientExperienceStats {
	total_submission_count: number;
	new_submission_count: number;
	completion_rate: number;
	submit_completion_count: number;
	submit_drop_off_count: number;
	average_rating: number;
	distribution: {
		"1": number;
		"2": number;
		"3": number;
		"4": number;
		"5": number;
	};
	total_reviews: number;
}

export interface PatientExperienceStatsResponse {
	success: boolean;
	data: PatientExperienceStats;
	message: string;
}

export interface FeedbackResponse {
	field_id: number;
	question: string;
	type: string;
	display_type: string;
	raw_value: string;
	formatted_value: string;
	value_type: string;
}

export interface PatientFeedbackDetail {
	question_id: string;
	question: string;
	type_label: string;
	type: string;
	station_name: string;
	location_name: string;
	service_name: string;
	total_response_count: number;
	general_feedback: string;
	responses: FeedbackResponse[];
}

export interface PatientFeedbackDetailResponse {
	success: boolean;
	data: PatientFeedbackDetail;
	message: string;
}

export interface FeedbackSummary {
	question_id: string;
	question: string;
	type_label: string;
	type: string;
	station_name: string;
	location_name: string;
	service_name: string;
	total_response_count: number;
}

export interface FeedbackSummariesResponse {
	success: boolean;
	data: FeedbackSummary[];
	message: string;
}

export const patientExperienceApi = {
	// Get patient experience feedbacks with filters
	getPatientExperience: async (
		filters: PatientExperienceFilters = {}
	): Promise<PatientExperienceResponse> => {
		const params = new URLSearchParams();

		// Add query parameters
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.location_ids && filters.location_ids.length > 0) {
			filters.location_ids.forEach((id) =>
				params.append("location_ids[]", id.toString())
			);
		}
		if (filters.category_ids && filters.category_ids.length > 0) {
			filters.category_ids.forEach((id) =>
				params.append("category_ids[]", id.toString())
			);
		}
		if (filters.station_ids && filters.station_ids.length > 0) {
			filters.station_ids.forEach((id) =>
				params.append("station_ids[]", id.toString())
			);
		}
		if (filters.service_ids && filters.service_ids.length > 0) {
			filters.service_ids.forEach((id) =>
				params.append("service_ids[]", id.toString())
			);
		}

		const queryString = params.toString();
		const url = queryString
			? `${PATIENT_EXPERIENCE_ENDPOINTS.base}?${queryString}`
			: PATIENT_EXPERIENCE_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	// Get patient experience statistics
	getPatientExperienceStats: async (
		organizationId: number
	): Promise<PatientExperienceStatsResponse> => {
		const response = await apiClient.get(
			PATIENT_EXPERIENCE_ENDPOINTS.stats,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Get detailed feedback by response UUID
	getPatientFeedbackDetail: async (
		responseUUId: string,
		organizationId: number
	): Promise<PatientFeedbackDetailResponse> => {
		const response = await apiClient.get(
			PATIENT_EXPERIENCE_ENDPOINTS.detail(responseUUId),
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Get feedback summaries with filters
	getFeedbackSummaries: async (
		filters: PatientExperienceFilters = {}
	): Promise<FeedbackSummariesResponse> => {
		const params = new URLSearchParams();

		// Add query parameters
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.location_ids && filters.location_ids.length > 0) {
			filters.location_ids.forEach((id) =>
				params.append("location_ids[]", id.toString())
			);
		}
		if (filters.category_ids && filters.category_ids.length > 0) {
			filters.category_ids.forEach((id) =>
				params.append("category_ids[]", id.toString())
			);
		}
		if (filters.station_ids && filters.station_ids.length > 0) {
			filters.station_ids.forEach((id) =>
				params.append("station_ids[]", id.toString())
			);
		}
		if (filters.service_ids && filters.service_ids.length > 0) {
			filters.service_ids.forEach((id) =>
				params.append("service_ids[]", id.toString())
			);
		}

		const queryString = params.toString();
		const url = queryString
			? `${PATIENT_EXPERIENCE_ENDPOINTS.summaries}?${queryString}`
			: PATIENT_EXPERIENCE_ENDPOINTS.summaries;

		const headers: Record<string, any> = {};
		if (filters.organization_id) {
			headers["X-organizationId"] = filters.organization_id;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},
};
